#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线连接逻辑推理题求解器
题目：将图中上方的方块与下方对应的方块用不相交的线连起来，且连线只能在图内

上方：A  B  C
下方：C  B  A

需要连接：A-A, B-B, C-C，且线不能相交
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
from typing import List, Tuple, Dict
import itertools

class LineConnectionSolver:
    def __init__(self):
        # 定义方块位置
        self.top_boxes = {
            'A': (1, 3),  # 上方A的位置
            'B': (2, 3),  # 上方B的位置  
            'C': (3, 3)   # 上方C的位置
        }
        
        self.bottom_boxes = {
            'C': (1, 1),  # 下方C的位置
            'B': (2, 1),  # 下方B的位置
            'A': (3, 1)   # 下方A的位置
        }
        
        # 需要连接的对应关系
        self.connections = [('A', 'A'), ('B', 'B'), ('C', 'C')]
        
    def analyze_problem(self):
        """分析问题结构"""
        print("=" * 60)
        print("                线连接逻辑推理题分析")
        print("=" * 60)
        
        print("\n📋 题目分析：")
        print("   上方方块排列：A  B  C")
        print("   下方方块排列：C  B  A")
        print("   需要连接：A-A, B-B, C-C")
        print("   约束条件：线不能相交，连线只能在图内")
        
        print(f"\n📍 方块位置坐标：")
        print("   上方：")
        for letter, pos in self.top_boxes.items():
            print(f"     {letter}: {pos}")
        print("   下方：")
        for letter, pos in self.bottom_boxes.items():
            print(f"     {letter}: {pos}")
        
        return True
    
    def check_line_intersection(self, line1, line2):
        """检查两条线段是否相交"""
        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])
        
        def intersect(A, B, C, D):
            return ccw(A, C, D) != ccw(B, C, D) and ccw(A, B, C) != ccw(A, B, D)
        
        return intersect(line1[0], line1[1], line2[0], line2[1])
    
    def find_valid_connections(self):
        """找到所有有效的连接方案"""
        print("\n🔍 寻找有效连接方案...")
        
        # 获取连接线段
        lines = []
        for connection in self.connections:
            top_letter, bottom_letter = connection
            top_pos = self.top_boxes[top_letter]
            bottom_pos = self.bottom_boxes[bottom_letter]
            lines.append((top_pos, bottom_pos, f"{top_letter}-{bottom_letter}"))
        
        print("   需要连接的线段：")
        for line in lines:
            print(f"     {line[2]}: {line[0]} -> {line[1]}")
        
        # 检查线段相交情况
        intersections = []
        for i in range(len(lines)):
            for j in range(i + 1, len(lines)):
                line1 = (lines[i][0], lines[i][1])
                line2 = (lines[j][0], lines[j][1])
                
                if self.check_line_intersection(line1, line2):
                    intersections.append((lines[i][2], lines[j][2]))
        
        print(f"\n❌ 直接连接的相交情况：")
        if intersections:
            for intersection in intersections:
                print(f"     {intersection[0]} 与 {intersection[1]} 相交")
        else:
            print("     无相交")
        
        return lines, intersections
    
    def solve_with_path_routing(self):
        """通过路径规划解决相交问题"""
        print("\n🛤️ 使用路径规划解决相交问题...")
        
        # 方案1：A线绕过
        solution1 = {
            'A-A': [(1, 3), (0.5, 2.5), (0.5, 1.5), (3, 1)],  # A绕左边
            'B-B': [(2, 3), (2, 1)],                            # B直连
            'C-C': [(3, 3), (1, 1)]                             # C直连
        }
        
        # 方案2：C线绕过
        solution2 = {
            'A-A': [(1, 3), (3, 1)],                            # A直连
            'B-B': [(2, 3), (2, 1)],                            # B直连
            'C-C': [(3, 3), (3.5, 2.5), (3.5, 1.5), (1, 1)]   # C绕右边
        }
        
        # 方案3：两条线都绕过
        solution3 = {
            'A-A': [(1, 3), (0.5, 2.5), (0.5, 1.5), (3, 1)],  # A绕左边
            'B-B': [(2, 3), (2, 1)],                            # B直连
            'C-C': [(3, 3), (3.5, 2.5), (3.5, 1.5), (1, 1)]   # C绕右边
        }
        
        solutions = [
            ("方案1：A线绕左边", solution1),
            ("方案2：C线绕右边", solution2), 
            ("方案3：A和C都绕过", solution3)
        ]
        
        print("   找到的解决方案：")
        for i, (name, solution) in enumerate(solutions, 1):
            print(f"     {i}. {name}")
            
        return solutions
    
    def validate_solution(self, solution):
        """验证解决方案的有效性"""
        print(f"\n✅ 验证解决方案...")
        
        # 检查所有路径是否相交
        paths = list(solution.values())
        
        for i in range(len(paths)):
            for j in range(i + 1, len(paths)):
                path1 = paths[i]
                path2 = paths[j]
                
                # 检查路径中的每个线段
                for k in range(len(path1) - 1):
                    for l in range(len(path2) - 1):
                        line1 = (path1[k], path1[k + 1])
                        line2 = (path2[l], path2[l + 1])
                        
                        if self.check_line_intersection(line1, line2):
                            return False, f"路径相交：{line1} 与 {line2}"
        
        return True, "所有路径都不相交"
    
    def visualize_solution(self, solution, title="连接方案"):
        """可视化解决方案"""
        print(f"\n🎨 生成可视化图表：{title}")
        
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        
        # 设置坐标轴
        ax.set_xlim(0, 4)
        ax.set_ylim(0, 4)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        
        # 绘制上方方块
        for letter, pos in self.top_boxes.items():
            rect = FancyBboxPatch((pos[0]-0.2, pos[1]-0.2), 0.4, 0.4,
                                boxstyle="round,pad=0.02", 
                                facecolor='lightblue', 
                                edgecolor='black')
            ax.add_patch(rect)
            ax.text(pos[0], pos[1], letter, ha='center', va='center', 
                   fontsize=14, fontweight='bold')
        
        # 绘制下方方块
        for letter, pos in self.bottom_boxes.items():
            rect = FancyBboxPatch((pos[0]-0.2, pos[1]-0.2), 0.4, 0.4,
                                boxstyle="round,pad=0.02", 
                                facecolor='lightgreen', 
                                edgecolor='black')
            ax.add_patch(rect)
            ax.text(pos[0], pos[1], letter, ha='center', va='center', 
                   fontsize=14, fontweight='bold')
        
        # 绘制连接线
        colors = ['red', 'blue', 'green']
        for i, (connection, path) in enumerate(solution.items()):
            color = colors[i % len(colors)]
            
            # 绘制路径
            for j in range(len(path) - 1):
                start = path[j]
                end = path[j + 1]
                ax.plot([start[0], end[0]], [start[1], end[1]], 
                       color=color, linewidth=2, label=connection if j == 0 else "")
        
        ax.set_title(title, fontsize=16, fontweight='bold')
        ax.legend()
        ax.set_xlabel('X坐标')
        ax.set_ylabel('Y坐标')
        
        # 保存图片
        filename = f"connection_solution_{title.replace('：', '_').replace(' ', '_')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"   图表已保存为：{filename}")
        
        return fig
    
    def comprehensive_solve(self):
        """综合求解"""
        print("=" * 60)
        print("                开始求解线连接问题")
        print("=" * 60)
        
        # 分析问题
        self.analyze_problem()
        
        # 找到连接方案
        lines, intersections = self.find_valid_connections()
        
        # 如果有相交，寻找绕行方案
        if intersections:
            print(f"\n⚠️ 检测到线段相交，需要寻找绕行方案")
            solutions = self.solve_with_path_routing()
            
            print(f"\n🎯 推荐解决方案：")
            
            best_solution = None
            for name, solution in solutions:
                is_valid, message = self.validate_solution(solution)
                print(f"   {name}: {'✅ 有效' if is_valid else '❌ 无效'} - {message}")
                
                if is_valid and best_solution is None:
                    best_solution = (name, solution)
            
            if best_solution:
                print(f"\n🏆 最佳方案：{best_solution[0]}")
                
                # 显示具体路径
                print("   具体连接路径：")
                for connection, path in best_solution[1].items():
                    print(f"     {connection}: {' -> '.join(map(str, path))}")
                
                # 生成可视化
                try:
                    self.visualize_solution(best_solution[1], best_solution[0])
                except Exception as e:
                    print(f"   可视化生成失败：{e}")
                
                return best_solution[1]
            else:
                print(f"\n❌ 未找到有效的解决方案")
                return None
        else:
            print(f"\n✅ 直接连接无相交，问题已解决")
            direct_solution = {
                'A-A': [self.top_boxes['A'], self.bottom_boxes['A']],
                'B-B': [self.top_boxes['B'], self.bottom_boxes['B']],
                'C-C': [self.top_boxes['C'], self.bottom_boxes['C']]
            }
            
            try:
                self.visualize_solution(direct_solution, "直接连接方案")
            except Exception as e:
                print(f"   可视化生成失败：{e}")
            
            return direct_solution
    
    def provide_manual_solution(self):
        """提供手工解题思路"""
        print(f"\n💡 手工解题思路：")
        print("=" * 60)
        
        steps = [
            "1. 观察上下方块的排列：上方A-B-C，下方C-B-A",
            "2. 识别需要连接的对应关系：A-A, B-B, C-C", 
            "3. 发现A-A和C-C的直线连接会相交",
            "4. 解决方案：让其中一条线绕过另一条线",
            "5. 推荐：A线从左边绕过，或C线从右边绕过",
            "6. 验证：确保所有连线都在图内且不相交"
        ]
        
        for step in steps:
            print(f"   {step}")
        
        print(f"\n🎯 最简单的解法：")
        print("   • A连A：从左上角的A向左下绕，再向右连到右下角的A")
        print("   • B连B：中间的B直接向下连到中间的B") 
        print("   • C连C：右上角的C直接斜向左下连到左下角的C")

def main():
    solver = LineConnectionSolver()
    solution = solver.comprehensive_solve()
    solver.provide_manual_solution()
    
    print(f"\n📝 总结：")
    print("   这是一个经典的线连接逻辑推理题")
    print("   关键在于识别相交问题并找到合适的绕行路径")
    print("   通过编程可以系统性地分析和验证所有可能的解决方案")

if __name__ == "__main__":
    main()
