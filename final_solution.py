#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解决方案：使用多种编程工具解决8位数问题

问题：将6个数2,0,1,9,20,19按任意次序排成一行，拼成一个8位数(首位不为0)
求：产生的不同的8位数的个数
"""

from itertools import permutations
from collections import Counter
import pandas as pd
import numpy as np

def solution_1_brute_force():
    """解法1：暴力枚举 + 集合去重"""
    print("=" * 50)
    print("解法1：暴力枚举 + 集合去重")
    print("=" * 50)
    
    numbers = [2, 0, 1, 9, 20, 19]
    valid_numbers = set()
    
    for perm in permutations(numbers):
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            valid_numbers.add(number_str)
    
    result = len(valid_numbers)
    print(f"结果：{result}")
    return result, valid_numbers

def solution_2_pandas():
    """解法2：使用pandas进行数据分析"""
    print("=" * 50)
    print("解法2：使用pandas进行数据分析")
    print("=" * 50)
    
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 生成所有排列
    all_perms = list(permutations(numbers))
    
    # 创建DataFrame
    df = pd.DataFrame(all_perms, columns=['pos1', 'pos2', 'pos3', 'pos4', 'pos5', 'pos6'])
    
    # 生成8位数字符串
    df['number_str'] = df.apply(lambda row: ''.join(str(x) for x in row), axis=1)
    
    # 筛选条件
    df['is_8_digit'] = df['number_str'].str.len() == 8
    df['first_not_zero'] = df['number_str'].str[0] != '0'
    df['is_valid'] = df['is_8_digit'] & df['first_not_zero']
    
    # 统计
    valid_df = df[df['is_valid']]
    unique_numbers = valid_df['number_str'].nunique()
    
    print(f"总排列数：{len(df)}")
    print(f"有效排列数：{len(valid_df)}")
    print(f"不同的8位数个数：{unique_numbers}")
    
    # 分析重复情况
    duplicates = valid_df['number_str'].value_counts()
    duplicates = duplicates[duplicates > 1]
    print(f"有重复的8位数个数：{len(duplicates)}")
    
    return unique_numbers

def solution_3_numpy():
    """解法3：使用numpy进行数值计算"""
    print("=" * 50)
    print("解法3：使用numpy进行数值计算")
    print("=" * 50)
    
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 生成所有排列
    all_perms = np.array(list(permutations(numbers)))
    print(f"排列数组形状：{all_perms.shape}")
    
    # 转换为字符串数组
    str_array = np.array([''.join(map(str, perm)) for perm in all_perms])
    
    # 筛选条件
    is_8_digit = np.array([len(s) == 8 for s in str_array])
    first_not_zero = np.array([s[0] != '0' for s in str_array])
    is_valid = is_8_digit & first_not_zero
    
    # 获取有效的8位数
    valid_numbers = str_array[is_valid]
    unique_numbers = np.unique(valid_numbers)
    
    print(f"有效8位数个数：{len(valid_numbers)}")
    print(f"不同的8位数个数：{len(unique_numbers)}")
    
    return len(unique_numbers)

def solution_4_mathematical():
    """解法4：数学分析（理论值）"""
    print("=" * 50)
    print("解法4：数学分析（理论值）")
    print("=" * 50)
    
    from math import factorial
    
    # 如果所有排列都产生不同的8位数，理论值应该是：
    total_arrangements = factorial(6)  # 6个元素的全排列
    invalid_arrangements = factorial(5)  # 首位为0的排列
    theoretical_max = total_arrangements - invalid_arrangements
    
    print(f"理论最大值（如果没有重复）：{theoretical_max}")
    
    # 但实际上会有重复，因为不同的排列可能产生相同的8位数
    print("实际值会小于理论值，因为存在重复的8位数")
    
    return theoretical_max

def solution_5_set_comprehension():
    """解法5：集合推导式"""
    print("=" * 50)
    print("解法5：集合推导式（最简洁）")
    print("=" * 50)
    
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 一行代码解决
    result = len({
        ''.join(map(str, perm)) 
        for perm in permutations(numbers) 
        if len(''.join(map(str, perm))) == 8 and str(perm[0]) != '0'
    })
    
    print(f"结果：{result}")
    return result

def analyze_duplicates(valid_numbers):
    """分析重复情况"""
    print("=" * 50)
    print("重复情况分析")
    print("=" * 50)
    
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 找出所有产生相同8位数的排列组合
    number_to_perms = {}
    
    for perm in permutations(numbers):
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            if number_str not in number_to_perms:
                number_to_perms[number_str] = []
            number_to_perms[number_str].append(perm)
    
    # 统计重复情况
    duplicates = {num: perms for num, perms in number_to_perms.items() if len(perms) > 1}
    
    print(f"总共不同的8位数：{len(number_to_perms)}")
    print(f"有重复的8位数个数：{len(duplicates)}")
    
    # 分析重复的原因
    duplicate_counts = Counter([len(perms) for perms in duplicates.values()])
    print(f"重复次数分布：{dict(duplicate_counts)}")
    
    # 显示几个重复的例子
    print(f"\n重复的例子（前5个）：")
    for i, (num_str, perms) in enumerate(list(duplicates.items())[:5]):
        print(f"{i+1}. {num_str} 由 {len(perms)} 个排列产生：")
        for perm in perms:
            print(f"   {perm}")

def main():
    print("6个数2,0,1,9,20,19排成8位数问题的多种解法")
    print("=" * 60)
    
    # 执行所有解法
    result1, valid_numbers = solution_1_brute_force()
    result2 = solution_2_pandas()
    result3 = solution_3_numpy()
    result4 = solution_4_mathematical()
    result5 = solution_5_set_comprehension()
    
    # 分析重复情况
    analyze_duplicates(valid_numbers)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("结果汇总")
    print("=" * 60)
    print(f"解法1（暴力枚举）：     {result1}")
    print(f"解法2（pandas分析）：   {result2}")
    print(f"解法3（numpy计算）：    {result3}")
    print(f"解法4（数学理论）：     {result4}")
    print(f"解法5（集合推导）：     {result5}")
    
    if result1 == result2 == result3 == result5:
        print(f"\n✅ 实际计算方法结果一致！")
        print(f"🎯 最终答案：{result1}")
        print(f"📊 理论最大值：{result4}")
        print(f"📉 重复导致的减少：{result4 - result1}")
    else:
        print(f"\n❌ 结果不一致，需要检查")

if __name__ == "__main__":
    main()
