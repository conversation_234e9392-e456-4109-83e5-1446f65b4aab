#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析8位数问题
"""

from itertools import permutations
from collections import Counter

def analyze_problem():
    """详细分析问题"""
    numbers = [2, 0, 1, 9, 20, 19]
    
    print("=" * 60)
    print("详细分析8位数问题")
    print("=" * 60)
    
    print(f"原始数字：{numbers}")
    
    # 生成所有排列并分析
    all_permutations = list(permutations(numbers))
    print(f"总排列数：{len(all_permutations)}")
    
    # 分析每个排列
    valid_8digit_numbers = []
    invalid_cases = []
    
    for perm in all_permutations:
        # 转换为字符串
        number_str = ''.join(str(num) for num in perm)
        
        # 检查是否为8位数
        if len(number_str) != 8:
            invalid_cases.append((perm, f"不是8位数，长度为{len(number_str)}"))
            continue
            
        # 检查首位是否为0
        if number_str[0] == '0':
            invalid_cases.append((perm, "首位为0"))
            continue
            
        valid_8digit_numbers.append(number_str)
    
    print(f"有效的8位数个数：{len(valid_8digit_numbers)}")
    print(f"无效情况个数：{len(invalid_cases)}")
    
    # 检查是否有重复的8位数
    number_counts = Counter(valid_8digit_numbers)
    duplicates = {num: count for num, count in number_counts.items() if count > 1}
    
    if duplicates:
        print(f"\n发现重复的8位数：")
        for num, count in duplicates.items():
            print(f"  {num}: 出现{count}次")
        
        # 找出产生重复的排列
        print(f"\n产生重复8位数的排列：")
        for duplicate_num in duplicates:
            print(f"\n数字 {duplicate_num} 由以下排列产生：")
            for perm in all_permutations:
                if ''.join(str(num) for num in perm) == duplicate_num:
                    print(f"  {perm}")
    else:
        print("\n没有重复的8位数")
    
    # 统计不同的8位数个数
    unique_numbers = len(set(valid_8digit_numbers))
    print(f"\n不同的8位数个数：{unique_numbers}")
    
    # 分析无效情况
    print(f"\n无效情况分析：")
    invalid_reasons = Counter([reason for _, reason in invalid_cases])
    for reason, count in invalid_reasons.items():
        print(f"  {reason}: {count}个")
    
    # 显示一些示例
    print(f"\n前10个有效的8位数：")
    unique_sorted = sorted(set(valid_8digit_numbers))
    for i, num in enumerate(unique_sorted[:10]):
        print(f"  {i+1}: {num}")
    
    return unique_numbers, duplicates

def mathematical_verification():
    """数学验证"""
    print("\n" + "=" * 60)
    print("数学验证")
    print("=" * 60)
    
    from math import factorial
    
    # 6个不同元素的排列
    total_arrangements = factorial(6)
    print(f"6个元素的全排列：{total_arrangements}")
    
    # 首位为0的排列
    first_zero_arrangements = factorial(5)
    print(f"首位为0的排列：{first_zero_arrangements}")
    
    # 有效排列
    valid_arrangements = total_arrangements - first_zero_arrangements
    print(f"有效排列（首位不为0）：{valid_arrangements}")
    
    return valid_arrangements

def check_specific_cases():
    """检查特定情况"""
    print("\n" + "=" * 60)
    print("检查特定情况")
    print("=" * 60)
    
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 检查是否所有排列都产生不同的8位数
    all_permutations = list(permutations(numbers))
    all_8digit_strings = []
    
    for perm in all_permutations:
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            all_8digit_strings.append(number_str)
    
    print(f"总共生成的8位数字符串：{len(all_8digit_strings)}")
    print(f"不同的8位数字符串：{len(set(all_8digit_strings))}")
    
    # 找出重复的原因
    from collections import defaultdict
    string_to_perms = defaultdict(list)
    
    for perm in all_permutations:
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            string_to_perms[number_str].append(perm)
    
    duplicates = {s: perms for s, perms in string_to_perms.items() if len(perms) > 1}
    
    if duplicates:
        print(f"\n重复的8位数及其对应的排列：")
        for num_str, perms in list(duplicates.items())[:5]:  # 只显示前5个
            print(f"\n{num_str}:")
            for perm in perms:
                print(f"  {perm}")
    
    return len(set(all_8digit_strings))

def main():
    unique_count1, duplicates = analyze_problem()
    math_count = mathematical_verification()
    unique_count2 = check_specific_cases()
    
    print("\n" + "=" * 60)
    print("最终结果")
    print("=" * 60)
    print(f"方法1（详细分析）：{unique_count1}")
    print(f"方法2（数学计算）：{math_count}")
    print(f"方法3（特定检查）：{unique_count2}")
    
    if duplicates:
        print(f"\n⚠️  发现了重复的8位数，这解释了为什么暴力枚举结果较小")
        print(f"实际不同的8位数个数：{unique_count1}")
    else:
        print(f"\n✅ 没有重复，所有方法应该得到相同结果")

if __name__ == "__main__":
    main()
