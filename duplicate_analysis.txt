重复情况详细分析
==================================================

总共有 90 个8位数存在重复

11920209 由以下 2 种排列产生：
  (1, 19, 2, 0, 20, 9)
  (1, 19, 20, 2, 0, 9)

11920920 由以下 2 种排列产生：
  (1, 19, 2, 0, 9, 20)
  (1, 19, 20, 9, 2, 0)

11992020 由以下 2 种排列产生：
  (1, 19, 9, 2, 0, 20)
  (1, 19, 9, 20, 2, 0)

12019209 由以下 2 种排列产生：
  (1, 2, 0, 19, 20, 9)
  (1, 20, 19, 2, 0, 9)

12019920 由以下 2 种排列产生：
  (1, 2, 0, 19, 9, 20)
  (1, 20, 19, 9, 2, 0)

12020199 由以下 2 种排列产生：
  (1, 2, 0, 20, 19, 9)
  (1, 20, 2, 0, 19, 9)

12020919 由以下 2 种排列产生：
  (1, 2, 0, 20, 9, 19)
  (1, 20, 2, 0, 9, 19)

12091920 由以下 2 种排列产生：
  (1, 2, 0, 9, 19, 20)
  (1, 20, 9, 19, 2, 0)

12092019 由以下 2 种排列产生：
  (1, 2, 0, 9, 20, 19)
  (1, 20, 9, 2, 0, 19)

19019202 由以下 2 种排列产生：
  (1, 9, 0, 19, 20, 2)
  (19, 0, 1, 9, 20, 2)

19019220 由以下 2 种排列产生：
  (1, 9, 0, 19, 2, 20)
  (19, 0, 1, 9, 2, 20)

19020192 由以下 2 种排列产生：
  (1, 9, 0, 20, 19, 2)
  (19, 0, 20, 1, 9, 2)

19020219 由以下 2 种排列产生：
  (1, 9, 0, 20, 2, 19)
  (19, 0, 20, 2, 1, 9)

19021920 由以下 2 种排列产生：
  (1, 9, 0, 2, 19, 20)
  (19, 0, 2, 1, 9, 20)

19022019 由以下 2 种排列产生：
  (1, 9, 0, 2, 20, 19)
  (19, 0, 2, 20, 1, 9)

19120209 由以下 2 种排列产生：
  (19, 1, 2, 0, 20, 9)
  (19, 1, 20, 2, 0, 9)

19120920 由以下 2 种排列产生：
  (19, 1, 2, 0, 9, 20)
  (19, 1, 20, 9, 2, 0)

19190202 由以下 2 种排列产生：
  (1, 9, 19, 0, 20, 2)
  (19, 1, 9, 0, 20, 2)

19190220 由以下 2 种排列产生：
  (1, 9, 19, 0, 2, 20)
  (19, 1, 9, 0, 2, 20)

19192002 由以下 2 种排列产生：
  (1, 9, 19, 20, 0, 2)
  (19, 1, 9, 20, 0, 2)

19192020 由以下 4 种排列产生：
  (1, 9, 19, 2, 0, 20)
  (1, 9, 19, 20, 2, 0)
  (19, 1, 9, 2, 0, 20)
  (19, 1, 9, 20, 2, 0)

19192200 由以下 2 种排列产生：
  (1, 9, 19, 2, 20, 0)
  (19, 1, 9, 2, 20, 0)

19200192 由以下 2 种排列产生：
  (1, 9, 20, 0, 19, 2)
  (19, 20, 0, 1, 9, 2)

19200219 由以下 2 种排列产生：
  (1, 9, 20, 0, 2, 19)
  (19, 20, 0, 2, 1, 9)

19201209 由以下 2 种排列产生：
  (19, 2, 0, 1, 20, 9)
  (19, 20, 1, 2, 0, 9)

19201902 由以下 2 种排列产生：
  (1, 9, 20, 19, 0, 2)
  (19, 20, 1, 9, 0, 2)

19201920 由以下 4 种排列产生：
  (1, 9, 2, 0, 19, 20)
  (1, 9, 20, 19, 2, 0)
  (19, 2, 0, 1, 9, 20)
  (19, 20, 1, 9, 2, 0)

19202019 由以下 4 种排列产生：
  (1, 9, 2, 0, 20, 19)
  (1, 9, 20, 2, 0, 19)
  (19, 2, 0, 20, 1, 9)
  (19, 20, 2, 0, 1, 9)

19202091 由以下 2 种排列产生：
  (19, 2, 0, 20, 9, 1)
  (19, 20, 2, 0, 9, 1)

19202190 由以下 2 种排列产生：
  (1, 9, 20, 2, 19, 0)
  (19, 20, 2, 1, 9, 0)

19209120 由以下 2 种排列产生：
  (19, 2, 0, 9, 1, 20)
  (19, 20, 9, 1, 2, 0)

19209201 由以下 2 种排列产生：
  (19, 2, 0, 9, 20, 1)
  (19, 20, 9, 2, 0, 1)

19219020 由以下 2 种排列产生：
  (1, 9, 2, 19, 0, 20)
  (19, 2, 1, 9, 0, 20)

19219200 由以下 2 种排列产生：
  (1, 9, 2, 19, 20, 0)
  (19, 2, 1, 9, 20, 0)

19220019 由以下 2 种排列产生：
  (1, 9, 2, 20, 0, 19)
  (19, 2, 20, 0, 1, 9)

19220190 由以下 2 种排列产生：
  (1, 9, 2, 20, 19, 0)
  (19, 2, 20, 1, 9, 0)

19912020 由以下 2 种排列产生：
  (19, 9, 1, 2, 0, 20)
  (19, 9, 1, 20, 2, 0)

19920120 由以下 2 种排列产生：
  (19, 9, 2, 0, 1, 20)
  (19, 9, 20, 1, 2, 0)

19920201 由以下 2 种排列产生：
  (19, 9, 2, 0, 20, 1)
  (19, 9, 20, 2, 0, 1)

20019192 由以下 2 种排列产生：
  (20, 0, 1, 9, 19, 2)
  (20, 0, 19, 1, 9, 2)

20019219 由以下 2 种排列产生：
  (20, 0, 1, 9, 2, 19)
  (20, 0, 19, 2, 1, 9)

20021919 由以下 2 种排列产生：
  (20, 0, 2, 1, 9, 19)
  (20, 0, 2, 19, 1, 9)

20119209 由以下 2 种排列产生：
  (2, 0, 1, 19, 20, 9)
  (20, 1, 19, 2, 0, 9)

20119920 由以下 2 种排列产生：
  (2, 0, 1, 19, 9, 20)
  (20, 1, 19, 9, 2, 0)

20120199 由以下 2 种排列产生：
  (2, 0, 1, 20, 19, 9)
  (20, 1, 2, 0, 19, 9)

20120919 由以下 2 种排列产生：
  (2, 0, 1, 20, 9, 19)
  (20, 1, 2, 0, 9, 19)

20190192 由以下 2 种排列产生：
  (20, 1, 9, 0, 19, 2)
  (20, 19, 0, 1, 9, 2)

20190219 由以下 2 种排列产生：
  (20, 1, 9, 0, 2, 19)
  (20, 19, 0, 2, 1, 9)

20191209 由以下 2 种排列产生：
  (2, 0, 19, 1, 20, 9)
  (20, 19, 1, 2, 0, 9)

20191902 由以下 2 种排列产生：
  (20, 1, 9, 19, 0, 2)
  (20, 19, 1, 9, 0, 2)

20191920 由以下 4 种排列产生：
  (2, 0, 1, 9, 19, 20)
  (2, 0, 19, 1, 9, 20)
  (20, 1, 9, 19, 2, 0)
  (20, 19, 1, 9, 2, 0)

20192019 由以下 4 种排列产生：
  (2, 0, 1, 9, 20, 19)
  (2, 0, 19, 20, 1, 9)
  (20, 1, 9, 2, 0, 19)
  (20, 19, 2, 0, 1, 9)

20192091 由以下 2 种排列产生：
  (2, 0, 19, 20, 9, 1)
  (20, 19, 2, 0, 9, 1)

20192190 由以下 2 种排列产生：
  (20, 1, 9, 2, 19, 0)
  (20, 19, 2, 1, 9, 0)

20199120 由以下 2 种排列产生：
  (2, 0, 19, 9, 1, 20)
  (20, 19, 9, 1, 2, 0)

20199201 由以下 2 种排列产生：
  (2, 0, 19, 9, 20, 1)
  (20, 19, 9, 2, 0, 1)

20201199 由以下 2 种排列产生：
  (2, 0, 20, 1, 19, 9)
  (20, 2, 0, 1, 19, 9)

20201919 由以下 4 种排列产生：
  (2, 0, 20, 1, 9, 19)
  (2, 0, 20, 19, 1, 9)
  (20, 2, 0, 1, 9, 19)
  (20, 2, 0, 19, 1, 9)

20201991 由以下 2 种排列产生：
  (2, 0, 20, 19, 9, 1)
  (20, 2, 0, 19, 9, 1)

20209119 由以下 2 种排列产生：
  (2, 0, 20, 9, 1, 19)
  (20, 2, 0, 9, 1, 19)

20209191 由以下 2 种排列产生：
  (2, 0, 20, 9, 19, 1)
  (20, 2, 0, 9, 19, 1)

20219019 由以下 2 种排列产生：
  (20, 2, 1, 9, 0, 19)
  (20, 2, 19, 0, 1, 9)

20219190 由以下 2 种排列产生：
  (20, 2, 1, 9, 19, 0)
  (20, 2, 19, 1, 9, 0)

20911920 由以下 2 种排列产生：
  (2, 0, 9, 1, 19, 20)
  (20, 9, 1, 19, 2, 0)

20912019 由以下 2 种排列产生：
  (2, 0, 9, 1, 20, 19)
  (20, 9, 1, 2, 0, 19)

20919120 由以下 2 种排列产生：
  (2, 0, 9, 19, 1, 20)
  (20, 9, 19, 1, 2, 0)

20919201 由以下 2 种排列产生：
  (2, 0, 9, 19, 20, 1)
  (20, 9, 19, 2, 0, 1)

20920119 由以下 2 种排列产生：
  (2, 0, 9, 20, 1, 19)
  (20, 9, 2, 0, 1, 19)

20920191 由以下 2 种排列产生：
  (2, 0, 9, 20, 19, 1)
  (20, 9, 2, 0, 19, 1)

21901920 由以下 2 种排列产生：
  (2, 1, 9, 0, 19, 20)
  (2, 19, 0, 1, 9, 20)

21902019 由以下 2 种排列产生：
  (2, 1, 9, 0, 20, 19)
  (2, 19, 0, 20, 1, 9)

21919020 由以下 2 种排列产生：
  (2, 1, 9, 19, 0, 20)
  (2, 19, 1, 9, 0, 20)

21919200 由以下 2 种排列产生：
  (2, 1, 9, 19, 20, 0)
  (2, 19, 1, 9, 20, 0)

21920019 由以下 2 种排列产生：
  (2, 1, 9, 20, 0, 19)
  (2, 19, 20, 0, 1, 9)

21920190 由以下 2 种排列产生：
  (2, 1, 9, 20, 19, 0)
  (2, 19, 20, 1, 9, 0)

22001919 由以下 2 种排列产生：
  (2, 20, 0, 1, 9, 19)
  (2, 20, 0, 19, 1, 9)

22019019 由以下 2 种排列产生：
  (2, 20, 1, 9, 0, 19)
  (2, 20, 19, 0, 1, 9)

22019190 由以下 2 种排列产生：
  (2, 20, 1, 9, 19, 0)
  (2, 20, 19, 1, 9, 0)

91192020 由以下 2 种排列产生：
  (9, 1, 19, 2, 0, 20)
  (9, 1, 19, 20, 2, 0)

91201920 由以下 2 种排列产生：
  (9, 1, 2, 0, 19, 20)
  (9, 1, 20, 19, 2, 0)

91202019 由以下 2 种排列产生：
  (9, 1, 2, 0, 20, 19)
  (9, 1, 20, 2, 0, 19)

91912020 由以下 2 种排列产生：
  (9, 19, 1, 2, 0, 20)
  (9, 19, 1, 20, 2, 0)

91920120 由以下 2 种排列产生：
  (9, 19, 2, 0, 1, 20)
  (9, 19, 20, 1, 2, 0)

91920201 由以下 2 种排列产生：
  (9, 19, 2, 0, 20, 1)
  (9, 19, 20, 2, 0, 1)

92011920 由以下 2 种排列产生：
  (9, 2, 0, 1, 19, 20)
  (9, 20, 1, 19, 2, 0)

92012019 由以下 2 种排列产生：
  (9, 2, 0, 1, 20, 19)
  (9, 20, 1, 2, 0, 19)

92019120 由以下 2 种排列产生：
  (9, 2, 0, 19, 1, 20)
  (9, 20, 19, 1, 2, 0)

92019201 由以下 2 种排列产生：
  (9, 2, 0, 19, 20, 1)
  (9, 20, 19, 2, 0, 1)

92020119 由以下 2 种排列产生：
  (9, 2, 0, 20, 1, 19)
  (9, 20, 2, 0, 1, 19)

92020191 由以下 2 种排列产生：
  (9, 2, 0, 20, 19, 1)
  (9, 20, 2, 0, 19, 1)

