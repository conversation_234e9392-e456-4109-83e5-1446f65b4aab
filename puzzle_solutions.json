[{"type": "数独类型", "grid": [[1, 2, 3], [4, 5, 6], [7, 8, 9]], "mapping": {"A": 1, "B": 2, "C": 3, "D": 4, "E": 5, "F": 6, "G": 7, "H": 8, "I": 9}}, {"type": "幻方类型", "grid": [[2, 7, 6], [9, 5, 1], [4, 3, 8]], "mapping": {"A": 2, "B": 7, "C": 6, "D": 9, "E": 5, "F": 1, "G": 4, "H": 3, "I": 8}, "magic_sum": 15}, {"type": "数学序列类型 - 等差数列", "grid": [[1, 2, 3], [4, 5, 6], [7, 8, 9]], "mapping": {"A": 1, "B": 2, "C": 3, "D": 4, "E": 5, "F": 6, "G": 7, "H": 8, "I": 9}, "sequence": [1, 2, 3, 4, 5, 6, 7, 8, 9]}, {"type": "模式识别类型 - 螺旋模式", "grid": [[1, 2, 3], [8, 9, 4], [7, 6, 5]], "mapping": {"A": 1, "B": 2, "C": 3, "D": 8, "E": 9, "F": 4, "G": 7, "H": 6, "I": 5}, "pattern": [1, 2, 3, 8, 9, 4, 7, 6, 5]}, {"type": "逻辑约束类型", "grid": [[1, 2, 3], [4, 9, 5], [6, 7, 8]], "mapping": {"A": 1, "B": 2, "C": 3, "D": 4, "E": 9, "F": 5, "G": 6, "H": 7, "I": 8}, "constraints": ["中心位置值最大", "角落位置值递增", "相邻位置值差为1", "对角线位置值相等"]}, {"type": "工作分配类型", "assignment": {"A": "财务部", "B": "人事部", "C": "技术部", "D": "市场部", "E": "协调中心", "F": "运营部", "G": "备用1", "H": "备用2", "I": "备用3"}, "departments": ["财务部", "人事部", "技术部", "市场部", "运营部"], "experts": ["专家A", "专家B", "专家C", "专家D", "专家E"], "description": "5个部门分配到网格中，E为协调中心"}]