#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将6个数2,0,1,9,20,19按任意次序排成一行，拼成一个8位数(首位不为0)
计算产生的不同的8位数的个数

分析：
- 数字：2, 0, 1, 9, 20, 19
- 20和19是两位数，其他是一位数
- 总共有 1+1+1+1+2+2 = 8 位数字
- 首位不能为0
"""

from itertools import permutations
from typing import List, Set

def solve_method1_brute_force():
    """方法1：暴力枚举所有排列"""
    numbers = [2, 0, 1, 9, 20, 19]
    valid_numbers = set()
    
    # 生成所有可能的排列
    for perm in permutations(numbers):
        # 将排列转换为字符串
        number_str = ''.join(str(num) for num in perm)
        
        # 检查是否为8位数且首位不为0
        if len(number_str) == 8 and number_str[0] != '0':
            valid_numbers.add(number_str)
    
    return len(valid_numbers), sorted(valid_numbers)

def solve_method2_mathematical():
    """方法2：数学分析法 - 重新分析"""
    print("重新分析问题：")
    print("6个数字：2, 0, 1, 9, 20, 19")
    print("这里的20和19是作为整体的数字，不是拆分的")
    print("所以我们有6个元素要排列，但要形成8位数")
    print("这意味着：2(1位) + 0(1位) + 1(1位) + 9(1位) + 20(2位) + 19(2位) = 8位")

    # 这是一个排列问题：6个不同元素的全排列
    from math import factorial

    total_arrangements = factorial(6)
    print(f"6个元素的全排列数：{total_arrangements}")

    # 减去首位为0的情况
    # 如果0在首位，剩下5个元素排列
    invalid_arrangements = factorial(5)
    print(f"首位为0的排列数：{invalid_arrangements}")

    valid_arrangements = total_arrangements - invalid_arrangements
    print(f"有效排列数（首位不为0）：{valid_arrangements}")

    return valid_arrangements

def solve_method3_step_by_step():
    """方法3：分步计算法 - 按首位分类"""
    print("\n分步计算分析：")
    print("6个元素：2, 0, 1, 9, 20, 19")
    print("按首位分类计算：")

    from math import factorial

    # 情况1：首位为1
    print("情况1：首位为1")
    # 剩余5个元素：2, 0, 9, 20, 19 的排列
    case1 = factorial(5)
    print(f"首位为1的排列数：{case1}")

    # 情况2：首位为2
    print("情况2：首位为2")
    # 剩余5个元素：0, 1, 9, 20, 19 的排列
    case2 = factorial(5)
    print(f"首位为2的排列数：{case2}")

    # 情况3：首位为9
    print("情况3：首位为9")
    # 剩余5个元素：2, 0, 1, 20, 19 的排列
    case3 = factorial(5)
    print(f"首位为9的排列数：{case3}")

    # 情况4：首位为20
    print("情况4：首位为20")
    # 剩余5个元素：2, 0, 1, 9, 19 的排列
    case4 = factorial(5)
    print(f"首位为20的排列数：{case4}")

    # 情况5：首位为19
    print("情况5：首位为19")
    # 剩余5个元素：2, 0, 1, 9, 20 的排列
    case5 = factorial(5)
    print(f"首位为19的排列数：{case5}")

    # 注意：首位不能为0

    total = case1 + case2 + case3 + case4 + case5
    print(f"总计：{case1} + {case2} + {case3} + {case4} + {case5} = {total}")

    return total

def verify_with_generation():
    """验证：生成一些示例来检查"""
    numbers = [2, 0, 1, 9, 20, 19]
    examples = []
    
    count = 0
    for perm in permutations(numbers):
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            examples.append(number_str)
            count += 1
            if count <= 10:  # 只显示前10个例子
                print(f"示例 {count}: {number_str}")
    
    return len(examples)

def main():
    print("=" * 60)
    print("计算6个数2,0,1,9,20,19排成8位数的不同个数")
    print("=" * 60)
    
    print("\n方法1：暴力枚举法")
    print("-" * 30)
    count1, all_numbers = solve_method1_brute_force()
    print(f"通过暴力枚举得到的结果：{count1}")
    
    print("\n方法2：数学分析法")
    print("-" * 30)
    count2 = solve_method2_mathematical()
    
    print("\n方法3：分步计算法")
    print("-" * 30)
    count3 = solve_method3_step_by_step()
    
    print("\n验证示例：")
    print("-" * 30)
    verify_count = verify_with_generation()
    
    print("\n" + "=" * 60)
    print("结果汇总：")
    print(f"方法1（暴力枚举）：{count1}")
    print(f"方法2（数学分析）：{count2}")
    print(f"方法3（分步计算）：{count3}")
    print(f"验证结果：{verify_count}")
    
    if count1 == count2 == count3 == verify_count:
        print(f"\n✅ 所有方法结果一致！答案是：{count1}")
    else:
        print(f"\n❌ 方法结果不一致，需要检查计算")
    
    # 显示前20个8位数作为示例
    print(f"\n前20个有效的8位数示例：")
    for i, num in enumerate(all_numbers[:20]):
        print(f"{i+1:2d}: {num}")
    
    if len(all_numbers) > 20:
        print(f"... (还有{len(all_numbers)-20}个)")

if __name__ == "__main__":
    main()
