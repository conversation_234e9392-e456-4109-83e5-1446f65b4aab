#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整解决方案：8位数问题
使用多种编程工具和方法
"""

from itertools import permutations
from collections import Counter
import json

def comprehensive_solution():
    """综合解决方案"""
    print("=" * 80)
    print("                    8位数问题完整解决方案")
    print("=" * 80)
    
    numbers = [2, 0, 1, 9, 20, 19]
    
    print(f"\n📋 问题：将6个数 {numbers} 按任意次序排成一行，")
    print("   拼成一个8位数(首位不为0)，求产生的不同的8位数的个数。")
    
    # 方法1：基础暴力枚举
    print(f"\n🔧 方法1：基础暴力枚举")
    valid_numbers = set()
    all_permutations = list(permutations(numbers))
    
    for perm in all_permutations:
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            valid_numbers.add(number_str)
    
    result1 = len(valid_numbers)
    print(f"   结果：{result1}")
    
    # 方法2：列表推导式
    print(f"\n🔧 方法2：列表推导式")
    result2 = len({
        ''.join(map(str, perm)) 
        for perm in permutations(numbers) 
        if len(''.join(map(str, perm))) == 8 and str(perm[0]) != '0'
    })
    print(f"   结果：{result2}")
    
    # 方法3：函数式编程
    print(f"\n🔧 方法3：函数式编程")
    def is_valid_8digit(perm):
        s = ''.join(map(str, perm))
        return len(s) == 8 and s[0] != '0'
    
    def perm_to_string(perm):
        return ''.join(map(str, perm))
    
    valid_perms = filter(is_valid_8digit, permutations(numbers))
    unique_strings = set(map(perm_to_string, valid_perms))
    result3 = len(unique_strings)
    print(f"   结果：{result3}")
    
    # 方法4：分步统计
    print(f"\n🔧 方法4：分步统计分析")
    total_perms = len(all_permutations)
    valid_perms_count = 0
    number_to_perms = {}
    
    for perm in all_permutations:
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            valid_perms_count += 1
            if number_str not in number_to_perms:
                number_to_perms[number_str] = []
            number_to_perms[number_str].append(perm)
    
    unique_count = len(number_to_perms)
    duplicate_count = sum(1 for perms in number_to_perms.values() if len(perms) > 1)
    
    print(f"   总排列数：{total_perms}")
    print(f"   有效排列数：{valid_perms_count}")
    print(f"   不同8位数：{unique_count}")
    print(f"   有重复的8位数：{duplicate_count}")
    
    # 方法5：数学理论分析
    print(f"\n🔧 方法5：数学理论分析")
    from math import factorial
    
    theoretical_total = factorial(6)  # 6个元素全排列
    theoretical_invalid = factorial(5)  # 首位为0的排列
    theoretical_valid = theoretical_total - theoretical_invalid
    
    print(f"   理论总排列：{theoretical_total}")
    print(f"   理论无效排列：{theoretical_invalid}")
    print(f"   理论有效排列：{theoretical_valid}")
    print(f"   实际不同8位数：{unique_count}")
    print(f"   重复导致减少：{theoretical_valid - unique_count}")
    
    # 详细分析重复情况
    print(f"\n📊 重复情况详细分析：")
    duplicate_stats = Counter([len(perms) for perms in number_to_perms.values()])
    
    for repeat_count, num_count in sorted(duplicate_stats.items()):
        if repeat_count == 1:
            print(f"   • {num_count} 个8位数各有 1 种排列（无重复）")
        else:
            print(f"   • {num_count} 个8位数各有 {repeat_count} 种排列")
    
    # 显示重复示例
    print(f"\n📝 重复示例（前5个）：")
    duplicates = {num: perms for num, perms in number_to_perms.items() if len(perms) > 1}
    
    for i, (num_str, perms) in enumerate(list(duplicates.items())[:5]):
        print(f"   {i+1}. {num_str} 由 {len(perms)} 种排列产生：")
        for j, perm in enumerate(perms):
            print(f"      {chr(97+j)}) {perm}")
    
    # 首位数字分析
    print(f"\n📈 首位数字分析：")
    first_digit_count = Counter([num[0] for num in number_to_perms.keys()])
    
    for digit in sorted(first_digit_count.keys()):
        count = first_digit_count[digit]
        percentage = count / unique_count * 100
        print(f"   首位为 {digit}：{count} 个 ({percentage:.1f}%)")
    
    # 验证所有方法结果一致性
    print(f"\n✅ 结果验证：")
    results = [result1, result2, result3, unique_count]
    if len(set(results)) == 1:
        print(f"   所有计算方法结果一致：{results[0]}")
        print(f"   🎯 最终答案：{results[0]}")
    else:
        print(f"   ❌ 结果不一致：{results}")
    
    # 保存详细结果到文件
    save_detailed_results(number_to_perms, duplicate_stats, first_digit_count)
    
    return results[0]

def save_detailed_results(number_to_perms, duplicate_stats, first_digit_count):
    """保存详细结果到文件"""
    
    # 保存所有8位数到文件
    with open('all_8digit_numbers.txt', 'w', encoding='utf-8') as f:
        f.write("所有不同的8位数（共498个）：\n")
        f.write("=" * 50 + "\n")
        
        sorted_numbers = sorted(number_to_perms.keys())
        for i, num in enumerate(sorted_numbers, 1):
            perm_count = len(number_to_perms[num])
            f.write(f"{i:3d}. {num} (由{perm_count}种排列产生)\n")
    
    # 保存重复情况分析
    with open('duplicate_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("重复情况详细分析\n")
        f.write("=" * 50 + "\n\n")
        
        duplicates = {num: perms for num, perms in number_to_perms.items() if len(perms) > 1}
        
        f.write(f"总共有 {len(duplicates)} 个8位数存在重复\n\n")
        
        for num_str, perms in sorted(duplicates.items()):
            f.write(f"{num_str} 由以下 {len(perms)} 种排列产生：\n")
            for perm in perms:
                f.write(f"  {perm}\n")
            f.write("\n")
    
    # 保存统计数据为JSON
    stats_data = {
        "total_unique_numbers": len(number_to_perms),
        "duplicate_statistics": dict(duplicate_stats),
        "first_digit_distribution": dict(first_digit_count),
        "sample_numbers": sorted(list(number_to_perms.keys()))[:20]
    }
    
    with open('statistics.json', 'w', encoding='utf-8') as f:
        json.dump(stats_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到文件：")
    print(f"   • all_8digit_numbers.txt - 所有498个不同的8位数")
    print(f"   • duplicate_analysis.txt - 重复情况详细分析")
    print(f"   • statistics.json - 统计数据")

def quick_verification():
    """快速验证"""
    print(f"\n🚀 快速验证：")
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 一行代码解决
    result = len({
        ''.join(map(str, p)) for p in permutations(numbers) 
        if len(''.join(map(str, p))) == 8 and str(p[0]) != '0'
    })
    
    print(f"   一行代码结果：{result}")
    return result

def main():
    result1 = comprehensive_solution()
    result2 = quick_verification()
    
    print(f"\n" + "=" * 80)
    print(f"🏆 最终确认答案：{result1}")
    print(f"🔍 快速验证结果：{result2}")
    
    if result1 == result2:
        print(f"✅ 验证通过！")
    else:
        print(f"❌ 验证失败！")
    
    print(f"\n📚 总结：")
    print(f"   使用了多种Python编程工具和方法：")
    print(f"   • itertools.permutations - 排列生成")
    print(f"   • collections.Counter - 统计分析")
    print(f"   • 集合推导式 - 简洁实现")
    print(f"   • 函数式编程 - 过滤和映射")
    print(f"   • 文件I/O - 结果保存")
    print(f"   • JSON - 数据序列化")
    print(f"   所有方法都得到了一致的结果：498")

if __name__ == "__main__":
    main()
