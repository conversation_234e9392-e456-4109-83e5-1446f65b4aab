#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成线连接问题的解决方案图表
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_solution_diagram():
    """创建解决方案图表"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 图1：问题展示
    create_problem_diagram(ax1)
    
    # 图2：解决方案
    create_solution_diagram_detailed(ax2)
    
    plt.tight_layout()
    plt.savefig('line_connection_solution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("图表已生成并保存为 'line_connection_solution.png'")

def create_problem_diagram(ax):
    """创建问题展示图"""
    ax.set_xlim(0, 4)
    ax.set_ylim(0, 5)
    ax.set_aspect('equal')
    
    # 绘制边界框
    boundary = patches.Rectangle((0.2, 0.2), 3.6, 4.6, 
                               linewidth=3, edgecolor='black', 
                               facecolor='lightgray', alpha=0.3)
    ax.add_patch(boundary)
    
    # 上方方块位置
    top_positions = {'A': (1, 4), 'B': (2, 4), 'C': (3, 4)}
    # 下方方块位置  
    bottom_positions = {'C': (1, 1), 'B': (2, 1), 'A': (3, 1)}
    
    # 绘制上方方块
    for letter, pos in top_positions.items():
        rect = FancyBboxPatch((pos[0]-0.2, pos[1]-0.2), 0.4, 0.4,
                            boxstyle="round,pad=0.05", 
                            facecolor='lightblue', 
                            edgecolor='navy', linewidth=2)
        ax.add_patch(rect)
        ax.text(pos[0], pos[1], letter, ha='center', va='center', 
               fontsize=16, fontweight='bold', color='navy')
    
    # 绘制下方方块
    for letter, pos in bottom_positions.items():
        rect = FancyBboxPatch((pos[0]-0.2, pos[1]-0.2), 0.4, 0.4,
                            boxstyle="round,pad=0.05", 
                            facecolor='lightgreen', 
                            edgecolor='darkgreen', linewidth=2)
        ax.add_patch(rect)
        ax.text(pos[0], pos[1], letter, ha='center', va='center', 
               fontsize=16, fontweight='bold', color='darkgreen')
    
    # 绘制直接连线（会相交的）
    # A-A 连线
    ax.plot([1, 3], [4, 1], 'r--', linewidth=2, alpha=0.7, label='A-A (会相交)')
    # B-B 连线
    ax.plot([2, 2], [4, 1], 'b--', linewidth=2, alpha=0.7, label='B-B (会相交)')
    # C-C 连线
    ax.plot([3, 1], [4, 1], 'purple', linestyle='--', linewidth=2, alpha=0.7, label='C-C (会相交)')
    
    # 标记相交点
    intersection_x = 2
    intersection_y = 2.5
    circle = Circle((intersection_x, intersection_y), 0.1, color='red', zorder=10)
    ax.add_patch(circle)
    ax.text(intersection_x + 0.3, intersection_y, '相交点!', fontsize=12, color='red', fontweight='bold')
    
    ax.set_title('问题：直接连线会相交', fontsize=16, fontweight='bold', color='red')
    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')

def create_solution_diagram_detailed(ax):
    """创建详细的解决方案图"""
    ax.set_xlim(0, 4)
    ax.set_ylim(0, 5)
    ax.set_aspect('equal')
    
    # 绘制边界框
    boundary = patches.Rectangle((0.2, 0.2), 3.6, 4.6, 
                               linewidth=3, edgecolor='black', 
                               facecolor='lightgray', alpha=0.3)
    ax.add_patch(boundary)
    
    # 上方方块位置
    top_positions = {'A': (1, 4), 'B': (2, 4), 'C': (3, 4)}
    # 下方方块位置
    bottom_positions = {'C': (1, 1), 'B': (2, 1), 'A': (3, 1)}
    
    # 绘制上方方块
    for letter, pos in top_positions.items():
        rect = FancyBboxPatch((pos[0]-0.2, pos[1]-0.2), 0.4, 0.4,
                            boxstyle="round,pad=0.05", 
                            facecolor='lightblue', 
                            edgecolor='navy', linewidth=2)
        ax.add_patch(rect)
        ax.text(pos[0], pos[1], letter, ha='center', va='center', 
               fontsize=16, fontweight='bold', color='navy')
    
    # 绘制下方方块
    for letter, pos in bottom_positions.items():
        rect = FancyBboxPatch((pos[0]-0.2, pos[1]-0.2), 0.4, 0.4,
                            boxstyle="round,pad=0.05", 
                            facecolor='lightgreen', 
                            edgecolor='darkgreen', linewidth=2)
        ax.add_patch(rect)
        ax.text(pos[0], pos[1], letter, ha='center', va='center', 
               fontsize=16, fontweight='bold', color='darkgreen')
    
    # 解决方案的连线路径
    
    # A-A 连线：绕左边
    a_path_x = [1, 0.4, 0.4, 3]
    a_path_y = [4, 3.5, 1.5, 1]
    ax.plot(a_path_x, a_path_y, 'red', linewidth=4, label='A-A (绕左边)', marker='o', markersize=6)
    
    # 添加箭头指示方向
    for i in range(len(a_path_x)-1):
        dx = a_path_x[i+1] - a_path_x[i]
        dy = a_path_y[i+1] - a_path_y[i]
        ax.annotate('', xy=(a_path_x[i+1], a_path_y[i+1]), 
                   xytext=(a_path_x[i], a_path_y[i]),
                   arrowprops=dict(arrowstyle='->', color='red', lw=2))
    
    # B-B 连线：直连
    ax.plot([2, 2], [4, 1], 'blue', linewidth=4, label='B-B (直连)', marker='o', markersize=6)
    ax.annotate('', xy=(2, 1), xytext=(2, 4),
               arrowprops=dict(arrowstyle='->', color='blue', lw=3))
    
    # C-C 连线：直连
    ax.plot([3, 1], [4, 1], 'purple', linewidth=4, label='C-C (直连)', marker='o', markersize=6)
    ax.annotate('', xy=(1, 1), xytext=(3, 4),
               arrowprops=dict(arrowstyle='->', color='purple', lw=3))
    
    # 添加路径说明
    ax.text(0.3, 2.5, 'A线绕行路径', fontsize=10, color='red', fontweight='bold', 
           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.text(2.2, 2.5, 'B线直连', fontsize=10, color='blue', fontweight='bold',
           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.text(1.8, 3.2, 'C线直连', fontsize=10, color='purple', fontweight='bold',
           bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.set_title('解决方案：A线绕左边，无相交', fontsize=16, fontweight='bold', color='green')
    ax.legend(loc='upper right')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('X坐标')
    ax.set_ylabel('Y坐标')

def create_step_by_step_diagram():
    """创建分步解决方案图"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    steps = [
        "步骤1：识别问题",
        "步骤2：分析相交",
        "步骤3：设计绕行",
        "步骤4：验证解决方案"
    ]
    
    for i, (ax, step) in enumerate(zip(axes.flat, steps)):
        create_step_diagram(ax, i+1, step)
    
    plt.tight_layout()
    plt.savefig('line_connection_steps.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("分步图表已生成并保存为 'line_connection_steps.png'")

def create_step_diagram(ax, step_num, title):
    """创建单个步骤图"""
    ax.set_xlim(0, 4)
    ax.set_ylim(0, 5)
    ax.set_aspect('equal')
    
    # 基本设置
    top_positions = {'A': (1, 4), 'B': (2, 4), 'C': (3, 4)}
    bottom_positions = {'C': (1, 1), 'B': (2, 1), 'A': (3, 1)}
    
    # 绘制方块
    for letter, pos in top_positions.items():
        rect = FancyBboxPatch((pos[0]-0.15, pos[1]-0.15), 0.3, 0.3,
                            boxstyle="round,pad=0.02", 
                            facecolor='lightblue', 
                            edgecolor='navy', linewidth=1)
        ax.add_patch(rect)
        ax.text(pos[0], pos[1], letter, ha='center', va='center', 
               fontsize=12, fontweight='bold')
    
    for letter, pos in bottom_positions.items():
        rect = FancyBboxPatch((pos[0]-0.15, pos[1]-0.15), 0.3, 0.3,
                            boxstyle="round,pad=0.02", 
                            facecolor='lightgreen', 
                            edgecolor='darkgreen', linewidth=1)
        ax.add_patch(rect)
        ax.text(pos[0], pos[1], letter, ha='center', va='center', 
               fontsize=12, fontweight='bold')
    
    # 根据步骤绘制不同内容
    if step_num == 1:
        # 步骤1：显示需要连接的对应关系
        ax.text(2, 2.5, 'A↔A\nB↔B\nC↔C', ha='center', va='center', 
               fontsize=14, fontweight='bold',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='yellow', alpha=0.7))
    
    elif step_num == 2:
        # 步骤2：显示相交的直线
        ax.plot([1, 3], [4, 1], 'r--', linewidth=2, alpha=0.7)
        ax.plot([2, 2], [4, 1], 'b--', linewidth=2, alpha=0.7)
        ax.plot([3, 1], [4, 1], 'purple', linestyle='--', linewidth=2, alpha=0.7)
        ax.text(2, 2.5, '相交!', ha='center', va='center', 
               fontsize=16, fontweight='bold', color='red')
    
    elif step_num == 3:
        # 步骤3：显示绕行设计
        a_path_x = [1, 0.5, 0.5, 3]
        a_path_y = [4, 3.5, 1.5, 1]
        ax.plot(a_path_x, a_path_y, 'red', linewidth=3, alpha=0.8)
        ax.text(0.3, 2.5, '绕行', ha='center', va='center', 
               fontsize=12, fontweight='bold', color='red')
    
    elif step_num == 4:
        # 步骤4：显示完整解决方案
        a_path_x = [1, 0.5, 0.5, 3]
        a_path_y = [4, 3.5, 1.5, 1]
        ax.plot(a_path_x, a_path_y, 'red', linewidth=3)
        ax.plot([2, 2], [4, 1], 'blue', linewidth=3)
        ax.plot([3, 1], [4, 1], 'purple', linewidth=3)
        ax.text(2, 0.3, '✓ 解决!', ha='center', va='center', 
               fontsize=14, fontweight='bold', color='green')
    
    ax.set_title(title, fontsize=12, fontweight='bold')
    ax.grid(True, alpha=0.3)

def main():
    """主函数"""
    print("正在生成线连接问题的解决方案图表...")
    
    # 生成主要解决方案图
    create_solution_diagram()
    
    # 生成分步解决方案图
    create_step_by_step_diagram()
    
    print("\n图表生成完成！")
    print("- line_connection_solution.png: 问题和解决方案对比图")
    print("- line_connection_steps.png: 分步解决过程图")

if __name__ == "__main__":
    main()
