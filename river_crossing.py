#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
过江问题求解器
爸爸、妈妈、两个儿子、两个女儿、奶奶，还有一条狗，要乘坐一艘船过江。

约束条件：
1. 只有爸爸、妈妈和奶奶会划船
2. 船每次只能容纳两个人
3. 安全规则：
   - 如果妈妈不在的话，爸爸会吃掉女儿
   - 爸爸不在的话，妈妈会吃掉儿子  
   - 奶奶不在的话，狗会吃了人
"""

from typing import List, Tuple, Set
from collections import deque

class RiverCrossing:
    def __init__(self):
        # 定义角色
        self.FATHER = "爸爸"
        self.MOTHER = "妈妈" 
        self.GRANDMOTHER = "奶奶"
        self.SON1 = "儿子1"
        self.SON2 = "儿子2"
        self.DAUGHTER1 = "女儿1"
        self.DAUGHTER2 = "女儿2"
        self.DOG = "狗"
        
        # 会划船的人
        self.ROWERS = {self.FATHER, self.MOTHER, self.GRANDMOTHER}
        
        # 所有角色
        self.ALL_PEOPLE = {
            self.FATHER, self.MOTHER, self.GRANDMOTHER,
            self.SON1, self.SON2, self.DAUGHTER1, self.DAUGHTER2, self.DOG
        }
        
        # 初始状态：所有人都在起始边
        self.initial_state = frozenset(self.ALL_PEOPLE)
        
    def is_safe_state(self, left_side: Set[str]) -> bool:
        """检查当前状态是否安全"""
        right_side = self.ALL_PEOPLE - left_side
        
        # 检查左边是否安全
        if not self._is_side_safe(left_side):
            return False
            
        # 检查右边是否安全
        if not self._is_side_safe(right_side):
            return False
            
        return True
    
    def _is_side_safe(self, side: Set[str]) -> bool:
        """检查一边是否安全"""
        if not side:  # 空的一边总是安全的
            return True
            
        # 规则1：如果妈妈不在的话，爸爸会吃掉女儿
        if (self.FATHER in side and 
            (self.DAUGHTER1 in side or self.DAUGHTER2 in side) and 
            self.MOTHER not in side):
            return False
            
        # 规则2：爸爸不在的话，妈妈会吃掉儿子
        if (self.MOTHER in side and 
            (self.SON1 in side or self.SON2 in side) and 
            self.FATHER not in side):
            return False
            
        # 规则3：奶奶不在的话，狗会吃了人
        if (self.DOG in side and 
            len(side) > 1 and  # 除了狗还有其他人
            self.GRANDMOTHER not in side):
            return False
            
        return True
    
    def get_valid_moves(self, left_side: Set[str]) -> List[Tuple[str, ...]]:
        """获取所有有效的移动"""
        moves = []
        people_on_left = list(left_side)
        
        # 单人过江（必须是会划船的人）
        for person in people_on_left:
            if person in self.ROWERS:
                moves.append((person,))
        
        # 两人过江（至少一个会划船）
        for i in range(len(people_on_left)):
            for j in range(i + 1, len(people_on_left)):
                person1, person2 = people_on_left[i], people_on_left[j]
                if person1 in self.ROWERS or person2 in self.ROWERS:
                    moves.append((person1, person2))
        
        return moves
    
    def apply_move(self, state: frozenset, move: Tuple[str, ...], boat_on_left: bool) -> frozenset:
        """应用移动，返回新状态"""
        if boat_on_left:
            # 船在左边，人从左边到右边
            return frozenset(state - set(move))
        else:
            # 船在右边，人从右边到左边
            return frozenset(state | set(move))
    
    def solve(self) -> List[Tuple[Tuple[str, ...], bool, frozenset]]:
        """使用BFS求解过江问题"""
        # (当前状态, 船是否在左边, 路径)
        queue = deque([(self.initial_state, True, [])])
        visited = set()
        visited.add((self.initial_state, True))
        
        while queue:
            current_state, boat_on_left, path = queue.popleft()
            
            # 检查是否达到目标状态（所有人都在右边）
            if len(current_state) == 0:
                return path
            
            # 获取当前可以移动的人
            if boat_on_left:
                available_people = current_state
            else:
                available_people = self.ALL_PEOPLE - current_state
            
            # 尝试所有可能的移动
            valid_moves = self.get_valid_moves(available_people)
            
            for move in valid_moves:
                # 应用移动
                new_state = self.apply_move(current_state, move, boat_on_left)
                new_boat_position = not boat_on_left
                
                # 检查新状态是否安全
                if self.is_safe_state(new_state):
                    state_key = (new_state, new_boat_position)
                    if state_key not in visited:
                        visited.add(state_key)
                        new_path = path + [(move, boat_on_left, new_state)]
                        queue.append((new_state, new_boat_position, new_path))
        
        return None  # 无解
    
    def print_solution(self, solution: List[Tuple[Tuple[str, ...], bool, frozenset]]):
        """打印解决方案"""
        if not solution:
            print("无解！")
            return
        
        print("过江方案：")
        print("=" * 50)
        
        # 初始状态
        print(f"初始状态：")
        print(f"左边：{', '.join(sorted(self.initial_state))}")
        print(f"右边：无")
        print(f"船位置：左边")
        print()
        
        for i, (move, boat_was_on_left, new_state) in enumerate(solution, 1):
            left_side = new_state
            right_side = self.ALL_PEOPLE - new_state
            
            if boat_was_on_left:
                direction = "从左边到右边"
                boat_position = "右边"
            else:
                direction = "从右边到左边"
                boat_position = "左边"
            
            print(f"第{i}步：{', '.join(move)} {direction}")
            print(f"左边：{', '.join(sorted(left_side)) if left_side else '无'}")
            print(f"右边：{', '.join(sorted(right_side)) if right_side else '无'}")
            print(f"船位置：{boat_position}")
            print()
        
        print("所有人安全过江！")

def main():
    solver = RiverCrossing()
    print("正在求解过江问题...")
    solution = solver.solve()
    solver.print_solution(solution)

if __name__ == "__main__":
    main()
