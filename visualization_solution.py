#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化解决方案
"""

import matplotlib.pyplot as plt
import seaborn as sns
from itertools import permutations
from collections import Counter
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_visualization():
    """创建可视化图表"""
    numbers = [2, 0, 1, 9, 20, 19]
    
    # 生成所有有效的8位数
    valid_numbers = []
    number_to_perms = {}
    
    for perm in permutations(numbers):
        number_str = ''.join(str(num) for num in perm)
        if len(number_str) == 8 and number_str[0] != '0':
            valid_numbers.append(number_str)
            if number_str not in number_to_perms:
                number_to_perms[number_str] = []
            number_to_perms[number_str].append(perm)
    
    # 统计重复次数
    duplicate_counts = Counter([len(perms) for perms in number_to_perms.values()])
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 图1：重复次数分布
    counts = list(duplicate_counts.keys())
    frequencies = list(duplicate_counts.values())
    ax1.bar(counts, frequencies, color='skyblue', edgecolor='navy')
    ax1.set_xlabel('重复次数')
    ax1.set_ylabel('8位数个数')
    ax1.set_title('8位数重复次数分布')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(frequencies):
        ax1.text(counts[i], v + 0.5, str(v), ha='center', va='bottom')
    
    # 图2：首位数字分布
    first_digits = [num[0] for num in set(valid_numbers)]
    first_digit_counts = Counter(first_digits)
    
    digits = list(first_digit_counts.keys())
    digit_counts = list(first_digit_counts.values())
    ax2.pie(digit_counts, labels=digits, autopct='%1.1f%%', startangle=90)
    ax2.set_title('首位数字分布')
    
    # 图3：解法结果对比
    methods = ['暴力枚举', 'pandas', 'numpy', '集合推导', '理论值']
    results = [498, 498, 498, 498, 600]
    colors = ['green' if r == 498 else 'orange' for r in results]
    
    bars = ax3.bar(methods, results, color=colors, edgecolor='black')
    ax3.set_ylabel('8位数个数')
    ax3.set_title('不同解法结果对比')
    ax3.set_ylim(450, 650)
    
    # 添加数值标签
    for bar, result in zip(bars, results):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{result}', ha='center', va='bottom')
    
    # 图4：8位数长度分布（验证都是8位）
    lengths = [len(num) for num in set(valid_numbers)]
    length_counts = Counter(lengths)
    
    ax4.bar(length_counts.keys(), length_counts.values(), color='lightcoral')
    ax4.set_xlabel('数字长度')
    ax4.set_ylabel('个数')
    ax4.set_title('生成数字长度分布（验证）')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('eight_digit_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return len(set(valid_numbers))

def create_summary_report():
    """创建总结报告"""
    print("=" * 80)
    print("                    8位数问题完整解决方案报告")
    print("=" * 80)
    
    print("\n📋 问题描述：")
    print("   将6个数 2, 0, 1, 9, 20, 19 按任意次序排成一行，")
    print("   拼成一个8位数(首位不为0)，求产生的不同的8位数的个数。")
    
    print("\n🔍 问题分析：")
    print("   • 6个数字：2(1位), 0(1位), 1(1位), 9(1位), 20(2位), 19(2位)")
    print("   • 总位数：1+1+1+1+2+2 = 8位")
    print("   • 约束条件：首位不能为0")
    print("   • 关键发现：不同的排列可能产生相同的8位数")
    
    print("\n💻 使用的编程工具：")
    print("   1. Python itertools.permutations - 生成排列")
    print("   2. Python collections.Counter - 统计重复")
    print("   3. pandas DataFrame - 数据分析")
    print("   4. numpy arrays - 数值计算")
    print("   5. matplotlib/seaborn - 数据可视化")
    print("   6. 集合推导式 - 简洁实现")
    
    print("\n📊 计算结果：")
    print("   • 理论最大值（无重复）：600")
    print("   • 实际不同8位数个数：498")
    print("   • 重复导致的减少：102")
    print("   • 重复情况：90个8位数有重复")
    print("     - 6个8位数各有4种排列产生")
    print("     - 84个8位数各有2种排列产生")
    
    print("\n✅ 验证方法：")
    print("   • 暴力枚举法：498")
    print("   • pandas分析法：498")
    print("   • numpy计算法：498")
    print("   • 集合推导法：498")
    print("   • 所有实际计算方法结果一致！")
    
    print("\n🎯 最终答案：498")
    
    print("\n📝 重复示例：")
    print("   20192019 可由以下4种排列产生：")
    print("   • (2, 0, 1, 9, 20, 19)")
    print("   • (2, 0, 19, 20, 1, 9)")
    print("   • (20, 1, 9, 2, 0, 19)")
    print("   • (20, 19, 2, 0, 1, 9)")
    
    print("\n" + "=" * 80)

def main():
    print("正在生成可视化图表...")
    result = create_visualization()
    
    print(f"\n可视化完成！图表已保存为 'eight_digit_analysis.png'")
    print(f"计算结果：{result}")
    
    create_summary_report()

if __name__ == "__main__":
    main()
