#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用逻辑推理题求解器
针对3x3网格的各种可能的逻辑推理问题
"""

from itertools import permutations, combinations
from typing import List, Dict, Tuple, Set
import json

class UniversalPuzzleSolver:
    def __init__(self):
        self.grid = [
            ['A', 'B', 'C'],
            ['D', 'E', 'F'],
            ['G', 'H', 'I']
        ]
        
        self.positions = {
            'A': (0, 0), 'B': (0, 1), 'C': (0, 2),
            'D': (1, 0), 'E': (1, 1), 'F': (1, 2),
            'G': (2, 0), 'H': (2, 1), 'I': (2, 2)
        }
        
        # 常见的逻辑推理题类型
        self.puzzle_types = [
            "数独类型", "幻方类型", "排列组合类型", 
            "工作分配类型", "逻辑关系类型", "数学序列类型"
        ]
    
    def solve_sudoku_type(self):
        """数独类型：每行每列不重复"""
        print("🔢 尝试数独类型解法...")
        
        # 3x3数独，使用1-9，每行每列每个3x3区域不重复
        def is_valid_sudoku(grid):
            # 检查行
            for row in grid:
                if len(set(row)) != 3:
                    return False
            
            # 检查列
            for col in range(3):
                column = [grid[row][col] for row in range(3)]
                if len(set(column)) != 3:
                    return False
            
            return True
        
        # 尝试1-9的排列
        for perm in permutations(range(1, 10)):
            test_grid = [
                [perm[0], perm[1], perm[2]],
                [perm[3], perm[4], perm[5]],
                [perm[6], perm[7], perm[8]]
            ]
            
            if is_valid_sudoku(test_grid):
                return {
                    "type": "数独类型",
                    "grid": test_grid,
                    "mapping": dict(zip(['A','B','C','D','E','F','G','H','I'], perm))
                }
        
        return None
    
    def solve_magic_square_type(self):
        """幻方类型：每行每列对角线和相等"""
        print("✨ 尝试幻方类型解法...")
        
        # 3x3幻方，使用1-9
        magic_squares = [
            [[2, 7, 6], [9, 5, 1], [4, 3, 8]],  # 标准幻方
            [[6, 1, 8], [7, 5, 3], [2, 9, 4]],  # 旋转变体
            [[8, 3, 4], [1, 5, 9], [6, 7, 2]],  # 另一个变体
        ]
        
        for magic_square in magic_squares:
            # 验证是否为幻方
            target_sum = sum(magic_square[0])
            
            # 检查所有行
            if all(sum(row) == target_sum for row in magic_square):
                # 检查所有列
                if all(sum(magic_square[i][j] for i in range(3)) == target_sum for j in range(3)):
                    # 检查对角线
                    diag1 = sum(magic_square[i][i] for i in range(3))
                    diag2 = sum(magic_square[i][2-i] for i in range(3))
                    
                    if diag1 == target_sum and diag2 == target_sum:
                        flat = [magic_square[i][j] for i in range(3) for j in range(3)]
                        mapping = dict(zip(['A','B','C','D','E','F','G','H','I'], flat))
                        
                        return {
                            "type": "幻方类型",
                            "grid": magic_square,
                            "mapping": mapping,
                            "magic_sum": target_sum
                        }
        
        return None
    
    def solve_sequence_type(self):
        """数学序列类型：按某种数学规律排列"""
        print("📈 尝试数学序列类型解法...")
        
        sequences = {
            "等差数列": list(range(1, 10)),
            "平方数列": [i*i for i in range(1, 10)],
            "斐波那契": [1, 1, 2, 3, 5, 8, 13, 21, 34],
            "质数序列": [2, 3, 5, 7, 11, 13, 17, 19, 23],
            "三角数": [1, 3, 6, 10, 15, 21, 28, 36, 45]
        }
        
        for seq_name, seq in sequences.items():
            if len(seq) >= 9:
                grid = [
                    [seq[0], seq[1], seq[2]],
                    [seq[3], seq[4], seq[5]],
                    [seq[6], seq[7], seq[8]]
                ]
                
                mapping = dict(zip(['A','B','C','D','E','F','G','H','I'], seq[:9]))
                
                return {
                    "type": f"数学序列类型 - {seq_name}",
                    "grid": grid,
                    "mapping": mapping,
                    "sequence": seq[:9]
                }
        
        return None
    
    def solve_pattern_recognition_type(self):
        """模式识别类型：基于位置关系的模式"""
        print("🎨 尝试模式识别类型解法...")
        
        patterns = {
            "螺旋模式": [1, 2, 3, 8, 9, 4, 7, 6, 5],
            "对称模式": [1, 2, 1, 2, 3, 2, 1, 2, 1],
            "中心辐射": [1, 2, 3, 2, 9, 2, 3, 2, 1],
            "棋盘模式": [1, 2, 1, 2, 1, 2, 1, 2, 1]
        }
        
        for pattern_name, pattern in patterns.items():
            grid = [
                [pattern[0], pattern[1], pattern[2]],
                [pattern[3], pattern[4], pattern[5]],
                [pattern[6], pattern[7], pattern[8]]
            ]
            
            mapping = dict(zip(['A','B','C','D','E','F','G','H','I'], pattern))
            
            return {
                "type": f"模式识别类型 - {pattern_name}",
                "grid": grid,
                "mapping": mapping,
                "pattern": pattern
            }
        
        return None
    
    def solve_logic_constraint_type(self):
        """逻辑约束类型：基于逻辑规则的推理"""
        print("🧠 尝试逻辑约束类型解法...")
        
        # 常见逻辑约束
        constraints = [
            "中心位置值最大",
            "角落位置值递增", 
            "相邻位置值差为1",
            "对角线位置值相等"
        ]
        
        # 尝试满足约束的分配
        def check_constraints(assignment):
            # 约束1：中心位置值最大
            center_val = assignment['E']
            if not all(center_val >= val for val in assignment.values()):
                return False
            
            # 约束2：角落位置值递增
            corners = [assignment['A'], assignment['C'], assignment['G'], assignment['I']]
            if corners != sorted(corners):
                return False
            
            return True
        
        # 尝试不同的分配
        for perm in permutations(range(1, 10)):
            assignment = dict(zip(['A','B','C','D','E','F','G','H','I'], perm))
            
            if check_constraints(assignment):
                grid = [
                    [assignment['A'], assignment['B'], assignment['C']],
                    [assignment['D'], assignment['E'], assignment['F']],
                    [assignment['G'], assignment['H'], assignment['I']]
                ]
                
                return {
                    "type": "逻辑约束类型",
                    "grid": grid,
                    "mapping": assignment,
                    "constraints": constraints
                }
        
        return None
    
    def solve_work_assignment_type(self):
        """工作分配类型：部门和专家的匹配"""
        print("👥 尝试工作分配类型解法...")
        
        # 5个部门，5个专家，9个位置
        departments = ['财务部', '人事部', '技术部', '市场部', '运营部']
        experts = ['专家A', '专家B', '专家C', '专家D', '专家E']
        
        # 分配策略
        assignment_strategy = {
            'A': '财务部', 'B': '人事部', 'C': '技术部',
            'D': '市场部', 'E': '协调中心', 'F': '运营部',
            'G': '备用1', 'H': '备用2', 'I': '备用3'
        }
        
        return {
            "type": "工作分配类型",
            "assignment": assignment_strategy,
            "departments": departments,
            "experts": experts,
            "description": "5个部门分配到网格中，E为协调中心"
        }
    
    def comprehensive_solve(self):
        """综合求解所有可能的类型"""
        print("=" * 80)
        print("                    通用逻辑推理题求解器")
        print("=" * 80)
        
        print(f"\n📋 分析3x3网格逻辑推理题：")
        print("   网格布局：")
        for row in self.grid:
            print(f"   {' '.join(row)}")
        
        print(f"\n🔍 尝试多种解题类型...")
        
        solutions = []
        
        # 尝试各种类型的解法
        solve_methods = [
            self.solve_sudoku_type,
            self.solve_magic_square_type,
            self.solve_sequence_type,
            self.solve_pattern_recognition_type,
            self.solve_logic_constraint_type,
            self.solve_work_assignment_type
        ]
        
        for method in solve_methods:
            try:
                solution = method()
                if solution:
                    solutions.append(solution)
            except Exception as e:
                print(f"   ⚠️ {method.__name__} 求解出错: {e}")
        
        # 显示所有找到的解
        print(f"\n🎯 找到的可能解法：")
        print("=" * 80)
        
        for i, solution in enumerate(solutions, 1):
            print(f"\n解法 {i}: {solution['type']}")
            
            if 'grid' in solution:
                print("   网格结果：")
                for row in solution['grid']:
                    print(f"   {row}")
            
            if 'mapping' in solution:
                print("   位置映射：")
                for pos in ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I']:
                    if pos in solution['mapping']:
                        print(f"   {pos}: {solution['mapping'][pos]}")
            
            if 'assignment' in solution:
                print("   分配结果：")
                for pos, assignment in solution['assignment'].items():
                    print(f"   {pos}: {assignment}")
            
            if 'magic_sum' in solution:
                print(f"   幻方和: {solution['magic_sum']}")
            
            if 'constraints' in solution:
                print("   约束条件：")
                for constraint in solution['constraints']:
                    print(f"   • {constraint}")
        
        # 提供最终建议
        self.provide_final_recommendation(solutions)
        
        # 保存结果
        self.save_solutions(solutions)
        
        return solutions
    
    def provide_final_recommendation(self, solutions):
        """提供最终建议"""
        print(f"\n🏆 最终建议：")
        print("=" * 80)
        
        if not solutions:
            print("   未找到明确的解，可能需要更多的约束条件信息")
            return
        
        # 根据解的类型给出建议
        solution_types = [sol['type'] for sol in solutions]
        
        print(f"   找到 {len(solutions)} 种可能的解法类型")
        
        if any("幻方" in t for t in solution_types):
            print("   🌟 推荐：幻方类型解法 - 数学性强，逻辑清晰")
        elif any("数独" in t for t in solution_types):
            print("   🌟 推荐：数独类型解法 - 约束明确，唯一解")
        elif any("工作分配" in t for t in solution_types):
            print("   🌟 推荐：工作分配解法 - 符合题目描述")
        else:
            print("   🌟 推荐：根据具体题目要求选择最合适的解法")
        
        print(f"\n💡 解题提示：")
        print("   1. 仔细阅读题目中的所有约束条件")
        print("   2. 确定题目类型（数学、逻辑、分配等）")
        print("   3. 使用排除法逐步缩小可能性")
        print("   4. 验证解的正确性和唯一性")
    
    def save_solutions(self, solutions):
        """保存解决方案到文件"""
        with open('puzzle_solutions.json', 'w', encoding='utf-8') as f:
            json.dump(solutions, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 解决方案已保存到 puzzle_solutions.json")

def main():
    solver = UniversalPuzzleSolver()
    solutions = solver.comprehensive_solve()
    
    print(f"\n📝 总结：")
    print("   这个通用求解器提供了多种可能的解法思路")
    print("   请根据题目的具体要求和约束条件选择最合适的解法")
    print("   如果能提供更详细的题目描述，可以给出更精确的解答")

if __name__ == "__main__":
    main()
